using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB.Architecture;
using View = Autodesk.Revit.DB.View;
using BecaRevitUtilities;
using Nice3point.Revit.Extensions;
using Autodesk.Revit.UI;

namespace MEP.SpaceDataExtractor.CoreLogic
{
    public class ZDE_Data_Rooms
    {
        #region Fields
        private RevitLinkInstance _linkInstance;
        private XYZ _internalOrigin;
        private bool _elementsLoaded;
        private bool _elementsAssigned;
        public List<ZDE_Element_Room> Elements;

        private bool _roomsLoaded;
        public List<ZDE_Room> Rooms;

        private IEnumerable<Level> _levelsToFilter;
        private IEnumerable<View> _viewsToFilter;
        #endregion

        #region Constructor
        public ZDE_Data_Rooms(IEnumerable<Level> levelsToFilter, IEnumerable<View> viewsToFilter)
        {
            _levelsToFilter = levelsToFilter;
            _viewsToFilter = viewsToFilter;

            Elements = new List<ZDE_Element_Room>();

            Rooms = new List<ZDE_Room>();
            _roomsLoaded = false;

            _elementsAssigned = false;
        }
        #endregion

        #region Methods
        /// <summary>
        /// 
        /// </summary>
        /// <param name="doc">Linked document</param>
        /// <param name="bics"></param>
        public void LoadAll(Document localDoc, Document linkedDoc, List<BuiltInCategory> bics)
        {
            // Find the correct link instance that matches the linked document
            _linkInstance = new FilteredElementCollector(localDoc)
                .OfClass(typeof(RevitLinkInstance))
                .Cast<RevitLinkInstance>()
                .FirstOrDefault(ri => ri.GetLinkDocument()?.Title == linkedDoc.Title);

            if (_linkInstance == null)
            {
                throw new Exception("Could not find the correct link instance for the linked document");
            }

            // Debug the transform
            Transform linkTransform = _linkInstance.GetTotalTransform();
            //Autodesk.Revit.UI.TaskDialog.Show("Link Transform Debug",
            //    $"Link Transform:\n" +
            //    $"Origin: {linkTransform.Origin}\n" +
            //    $"BasisX: {linkTransform.BasisX}\n" +
            //    $"BasisY: {linkTransform.BasisY}\n" +
            //    $"BasisZ: {linkTransform.BasisZ}\n" +
            //    $"Scale: {linkTransform.Scale}");

            _internalOrigin = new XYZ(0, 0, 0);

            LoadRooms(linkedDoc);
            LoadElements(localDoc, bics);
            AssignElements();

            return;
        }

        private void AssignElements()
        {
            ZDE_Room r;

            if (_elementsAssigned)
                return;

            if (!_elementsLoaded || !_roomsLoaded)
            {
                return;
            }

            foreach (var item in Rooms)
            {
                item.ClearElements();
            }
           
            foreach (var item in Elements)
            {
                if ((r = FindElementsRooms(item)) != null)
                {
                    r.AddElement(item);
                }
            }

            _elementsAssigned = true;
        }

        private ZDE_Room FindElementsRooms(ZDE_Element_Room e)
        {
            try
            {
                // Get the element's location point in host coordinates
                XYZ elementLocation = null;
                if (e.E.Location is LocationPoint lp)
                {
                    elementLocation = lp.Point;
                }
                else if (e.E.get_BoundingBox(null) != null)
                {
                    BoundingBoxXYZ bb = e.E.get_BoundingBox(null);
                    elementLocation = (bb.Min + bb.Max) * 0.5;
                }

                if (elementLocation == null) return null;

                // CRITICAL: Use a different approach for coordinate transformation
                // Method 1: Direct transform without internal origin adjustment
                Transform linkTransform = _linkInstance.GetTotalTransform();
                XYZ elementInLinkedCoords_Method1 = linkTransform.Inverse.OfPoint(elementLocation);

                // Method 2: With internal origin adjustment (your original approach)
                Document hostDoc = e.E.Document;
                Document linkedDoc = _linkInstance.GetLinkDocument();

                ProjectLocation hostProjectLocation = hostDoc.ActiveProjectLocation;
                ProjectPosition hostPosition = hostProjectLocation.GetProjectPosition(XYZ.Zero);
                ProjectLocation linkedProjectLocation = linkedDoc.ActiveProjectLocation;
                ProjectPosition linkedPosition = linkedProjectLocation.GetProjectPosition(XYZ.Zero);

                XYZ hostInternalOrigin = new XYZ(hostPosition.EastWest, hostPosition.NorthSouth, hostPosition.Elevation);
                XYZ linkedInternalOrigin = new XYZ(linkedPosition.EastWest, linkedPosition.NorthSouth, linkedPosition.Elevation);
                XYZ originDifference = hostInternalOrigin.Subtract(linkedInternalOrigin);

                XYZ adjustedLocation = elementLocation.Subtract(originDifference);
                XYZ elementInLinkedCoords_Method2 = linkTransform.Inverse.OfPoint(adjustedLocation);

                // Method 3: Try using the shared coordinates approach
                XYZ elementInLinkedCoords_Method3 = null;
                try
                {
                    // Get shared coordinate system transform
                    Transform sharedTransform = Transform.Identity;
                    if (hostDoc.ActiveProjectLocation.GetTransform() != null)
                    {
                        sharedTransform = hostDoc.ActiveProjectLocation.GetTransform();
                    }

                    // Transform to shared coordinates first, then to linked
                    XYZ elementInSharedCoords = sharedTransform.OfPoint(elementLocation);
                    elementInLinkedCoords_Method3 = linkTransform.Inverse.OfPoint(elementInSharedCoords);
                }
                catch
                {
                    elementInLinkedCoords_Method3 = elementInLinkedCoords_Method1;
                }

                // Debug all methods
                System.Diagnostics.Debug.WriteLine($"=== Element ID: {e.E.Id} ===");
                System.Diagnostics.Debug.WriteLine($"Original location: {elementLocation}");
                System.Diagnostics.Debug.WriteLine($"Method 1 (Direct): {elementInLinkedCoords_Method1}");
                System.Diagnostics.Debug.WriteLine($"Method 2 (Origin Adj): {elementInLinkedCoords_Method2}");
                System.Diagnostics.Debug.WriteLine($"Method 3 (Shared): {elementInLinkedCoords_Method3}");

                // Try all three methods
                List<XYZ> candidatePoints = new List<XYZ>
                {
                    elementInLinkedCoords_Method1,
                    elementInLinkedCoords_Method2,
                    elementInLinkedCoords_Method3
                };

                // For each candidate point, create comprehensive test points
                List<XYZ> allTestPoints = new List<XYZ>();

                foreach (var candidatePoint in candidatePoints)
                {
                    if (candidatePoint == null) continue;

                    // Add the candidate point itself
                    allTestPoints.Add(candidatePoint);

                    // Add points at room level elevations (most important for high Z coordinates)
                    foreach (var room in Rooms.Take(10)) // Check first 10 rooms for typical elevations
                    {
                        if (room.SpcRoom.Level != null)
                        {
                            double roomLevelZ = room.SpcRoom.Level.Elevation;
                            allTestPoints.Add(new XYZ(candidatePoint.X, candidatePoint.Y, roomLevelZ));
                            allTestPoints.Add(new XYZ(candidatePoint.X, candidatePoint.Y, roomLevelZ + 1.0)); // 1 foot above floor
                            allTestPoints.Add(new XYZ(candidatePoint.X, candidatePoint.Y, roomLevelZ + 8.0)); // Typical ceiling height
                        }
                    }

                    // Add horizontal offsets
                    double[] offsets = { 0.1, 0.5, 1.0, -0.1, -0.5, -1.0 };
                    foreach (double offset in offsets)
                    {
                        allTestPoints.Add(new XYZ(candidatePoint.X + offset, candidatePoint.Y, candidatePoint.Z));
                        allTestPoints.Add(new XYZ(candidatePoint.X, candidatePoint.Y + offset, candidatePoint.Z));
                    }
                }

                System.Diagnostics.Debug.WriteLine($"Testing {allTestPoints.Count} points total");

                // Check each room with all test points
                foreach (var room in Rooms)
                {
                    if (room.SpcRoom.Area < 1) continue;

                    // Get room info for debugging
                    BoundingBoxXYZ roomBB = room.SpcRoom.get_BoundingBox(null);
                    string roomInfo = $"Room: {room.SpcRoom.Name} (ID: {room.SpcRoom.Id})";
                    if (roomBB != null)
                    {
                        roomInfo += $" BB: ({roomBB.Min.X:F1}, {roomBB.Min.Y:F1}, {roomBB.Min.Z:F1}) to ({roomBB.Max.X:F1}, {roomBB.Max.Y:F1}, {roomBB.Max.Z:F1})";
                    }

                    // Try each test point
                    foreach (var point in allTestPoints)
                    {
                        try
                        {
                            if (room.SpcRoom.IsPointInRoom(point))
                            {
                                System.Diagnostics.Debug.WriteLine($"SUCCESS: Element {e.E.Id} found in {roomInfo} at point {point}");
                                e.AssignHostRoom(room);
                                return room;
                            }
                        }
                        catch (Exception ex)
                        {
                            // Don't spam debug with every failed point, just continue
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"No room found for element {e.E.Id} after testing {allTestPoints.Count} points");
                return null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error in FindElementsRooms: {ex.Message}\n{ex.StackTrace}");
                return null;
            }
        }

        private XYZ TransformToLinkedModel(XYZ hostPoint)
        {
            if (_linkInstance == null)
            {
                return null;
            }

            // Get the transformation matrix to map host coordinates to linked model coordinates
            Transform transformToLinked = _linkInstance.GetTransform().Inverse;

            // Transform the point
            return transformToLinked.OfPoint(hostPoint);
        }


        private void LoadElements(Document localDoc, List<BuiltInCategory> bics)
        {
            if (_elementsLoaded)
                return;

            Elements.Clear();

            foreach (var item in bics)
            {
                List<Element> filteredElements = ZDE_Utility.GetFilteredElements(_levelsToFilter, _viewsToFilter, localDoc, item);

                foreach (var e in filteredElements)
                {
                    ZDE_Element_Room element = new ZDE_Element_Room(localDoc, e);
                    Elements.Add(element);
                }
            }

            Elements = Elements.OrderBy(x => x.Level).ToList();

            _elementsLoaded = true;
        }

        private void LoadRooms(Document linkedDoc)
        {
            const double ft2m = 0.3048;

            if (_roomsLoaded)
                return;

            Rooms.Clear();

            var rooms = new FilteredElementCollector(linkedDoc).OfCategory(BuiltInCategory.OST_Rooms).Cast<Room>().Where(r => RevitUnitConvertor.InternalToSquareMeters(r.Area) > 0);

            // Filter by level and view if any
            rooms = ZDE_Utility.FilterRoomsByLevels(_levelsToFilter, rooms);
            rooms = ZDE_Utility.FilterRoomsByViews(_viewsToFilter, rooms);

            foreach ( var r in rooms)
            {
                if (r.Area >= 1 * ft2m * ft2m)
                {
                    ZDE_Room b = new ZDE_Room(r);
                    Rooms.Add(b);
                }
            }

            Rooms = Rooms.OrderBy(x => x.Level).ThenBy(x => x.Num).ToList();

            _roomsLoaded = true;
        }

        #endregion
    }
}
