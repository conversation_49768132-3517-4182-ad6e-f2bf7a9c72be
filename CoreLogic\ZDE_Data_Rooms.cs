using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB.Architecture;
using View = Autodesk.Revit.DB.View;
using BecaRevitUtilities;
using Nice3point.Revit.Extensions;
using Autodesk.Revit.UI;

namespace MEP.SpaceDataExtractor.CoreLogic
{
    public class ZDE_Data_Rooms
    {
        #region Fields
        private RevitLinkInstance _linkInstance;
        private XYZ _internalOrigin;
        private bool _elementsLoaded;
        private bool _elementsAssigned;
        public List<ZDE_Element_Room> Elements;

        private bool _roomsLoaded;
        public List<ZDE_Room> Rooms;

        private IEnumerable<Level> _levelsToFilter;
        private IEnumerable<View> _viewsToFilter;
        #endregion

        #region Constructor
        public ZDE_Data_Rooms(IEnumerable<Level> levelsToFilter, IEnumerable<View> viewsToFilter)
        {
            _levelsToFilter = levelsToFilter;
            _viewsToFilter = viewsToFilter;

            Elements = new List<ZDE_Element_Room>();

            Rooms = new List<ZDE_Room>();
            _roomsLoaded = false;

            _elementsAssigned = false;
        }
        #endregion

        #region Methods
        /// <summary>
        /// 
        /// </summary>
        /// <param name="doc">Linked document</param>
        /// <param name="bics"></param>
        public void LoadAll(Document localDoc, Document linkedDoc, List<BuiltInCategory> bics)
        {
            // Find the correct link instance that matches the linked document
            _linkInstance = new FilteredElementCollector(localDoc)
                .OfClass(typeof(RevitLinkInstance))
                .Cast<RevitLinkInstance>()
                .FirstOrDefault(ri => ri.GetLinkDocument()?.Title == linkedDoc.Title);

            if (_linkInstance == null)
            {
                throw new Exception("Could not find the correct link instance for the linked document");
            }

            // Debug the transform
            Transform linkTransform = _linkInstance.GetTotalTransform();
            //Autodesk.Revit.UI.TaskDialog.Show("Link Transform Debug",
            //    $"Link Transform:\n" +
            //    $"Origin: {linkTransform.Origin}\n" +
            //    $"BasisX: {linkTransform.BasisX}\n" +
            //    $"BasisY: {linkTransform.BasisY}\n" +
            //    $"BasisZ: {linkTransform.BasisZ}\n" +
            //    $"Scale: {linkTransform.Scale}");

            _internalOrigin = new XYZ(0, 0, 0);

            LoadRooms(linkedDoc);
            LoadElements(localDoc, bics);
            AssignElements();

            return;
        }

        private void AssignElements()
        {
            ZDE_Room r;

            if (_elementsAssigned)
                return;

            if (!_elementsLoaded || !_roomsLoaded)
            {
                return;
            }

            foreach (var item in Rooms)
            {
                item.ClearElements();
            }
           
            foreach (var item in Elements)
            {
                if ((r = FindElementsRooms(item)) != null)
                {
                    r.AddElement(item);
                }
            }

            _elementsAssigned = true;
        }

        private ZDE_Room FindElementsRooms(ZDE_Element_Room e)
        {
            try
            {
                // Get the element's location point in host coordinates
                XYZ elementLocation = null;
                if (e.E.Location is LocationPoint lp)
                {
                    elementLocation = lp.Point;
                }
                else if (e.E.get_BoundingBox(null) != null)
                {
                    BoundingBoxXYZ bb = e.E.get_BoundingBox(null);
                    elementLocation = (bb.Min + bb.Max) * 0.5;
                }

                if (elementLocation == null) return null;

                // Use direct transform - this is the method that works!
                Transform linkTransform = _linkInstance.GetTotalTransform();
                XYZ elementInLinkedCoords = linkTransform.Inverse.OfPoint(elementLocation);

                // Create test points for robust detection
                List<XYZ> testPoints = new List<XYZ>();

                // Add the main transformed point
                testPoints.Add(elementInLinkedCoords);

                // Add small horizontal offsets for boundary cases
                double[] offsets = { 0.1, 0.5, 1.0, -0.1, -0.5, -1.0 };
                foreach (double offset in offsets)
                {
                    testPoints.Add(new XYZ(elementInLinkedCoords.X + offset, elementInLinkedCoords.Y, elementInLinkedCoords.Z));
                    testPoints.Add(new XYZ(elementInLinkedCoords.X, elementInLinkedCoords.Y + offset, elementInLinkedCoords.Z));
                }

                // Check each room
                foreach (var room in Rooms)
                {
                    if (room.SpcRoom.Area < 1) continue;

                    // Try each test point
                    foreach (var point in testPoints)
                    {
                        try
                        {
                            if (room.SpcRoom.IsPointInRoom(point))
                            {
                                e.AssignHostRoom(room);
                                return room;
                            }
                        }
                        catch (Exception ex)
                        {
                            // Continue to next point if IsPointInRoom fails
                        }
                    }
                }

                return null;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error in FindElementsRooms: {ex.Message}\n{ex.StackTrace}");
                return null;
            }
        }

        private XYZ TransformToLinkedModel(XYZ hostPoint)
        {
            if (_linkInstance == null)
            {
                return null;
            }

            // Get the transformation matrix to map host coordinates to linked model coordinates
            Transform transformToLinked = _linkInstance.GetTransform().Inverse;

            // Transform the point
            return transformToLinked.OfPoint(hostPoint);
        }


        private void LoadElements(Document localDoc, List<BuiltInCategory> bics)
        {
            if (_elementsLoaded)
                return;

            Elements.Clear();

            foreach (var item in bics)
            {
                List<Element> filteredElements = ZDE_Utility.GetFilteredElements(_levelsToFilter, _viewsToFilter, localDoc, item);

                foreach (var e in filteredElements)
                {
                    ZDE_Element_Room element = new ZDE_Element_Room(localDoc, e);
                    Elements.Add(element);
                }
            }

            Elements = Elements.OrderBy(x => x.Level).ToList();

            _elementsLoaded = true;
        }

        private void LoadRooms(Document linkedDoc)
        {
            const double ft2m = 0.3048;

            if (_roomsLoaded)
                return;

            Rooms.Clear();

            var rooms = new FilteredElementCollector(linkedDoc).OfCategory(BuiltInCategory.OST_Rooms).Cast<Room>().Where(r => RevitUnitConvertor.InternalToSquareMeters(r.Area) > 0);

            // Filter by level and view if any
            rooms = ZDE_Utility.FilterRoomsByLevels(_levelsToFilter, rooms);
            rooms = ZDE_Utility.FilterRoomsByViews(_viewsToFilter, rooms);

            foreach ( var r in rooms)
            {
                if (r.Area >= 1 * ft2m * ft2m)
                {
                    ZDE_Room b = new ZDE_Room(r);
                    Rooms.Add(b);
                }
            }

            Rooms = Rooms.OrderBy(x => x.Level).ThenBy(x => x.Num).ToList();

            _roomsLoaded = true;
        }

        #endregion
    }
}
