﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
	  <UseWindowsForms>true</UseWindowsForms>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="UI\ModelessRevitForm\**" />
    <EmbeddedResource Remove="UI\ModelessRevitForm\**" />
    <None Remove="UI\ModelessRevitForm\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\COMMON\BecaActivityLogger\BecaActivityLogger.csproj" />
    <ProjectReference Include="..\..\COMMON\BecaCommand\BecaCommand.csproj" />
    <ProjectReference Include="..\..\COMMON\BecaRevitUtilities\BecaRevitUtilities.csproj" />
    <ProjectReference Include="..\..\COMMON\BecaTransactionsNames\BecaTransactionsNamesManager.csproj" />
    <ProjectReference Include="..\..\COMMON\BecaTrekaHandler\BecaTrekaHandler.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.EnhancedADGV\Common.EnhancedADGV.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.Extenstions\Common.Extenstions.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.UI\Common.UI.csproj" />
    <ProjectReference Include="..\..\COMMON\Common.Utilities\Common.Utilities.csproj" />
    <ProjectReference Include="..\..\GEN\GEN.LinkedFileMapper\GEN.LinkedFileMapper.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Common.BecaLicense">
      <HintPath>..\..\3rdParties\BecaLicensing\Common.BecaLicense.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>