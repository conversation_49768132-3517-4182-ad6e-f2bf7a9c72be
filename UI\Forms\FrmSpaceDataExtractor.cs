﻿using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using MEP.SpaceDataExtractor.CoreLogic;
using Autodesk.Revit.DB;
using MEP.SpaceDataExtractor.RevitCommands;
using Autodesk.Revit.DB.Mechanical;
using View = Autodesk.Revit.DB.View;

namespace MEP.SpaceDataExtractor.UI.Forms
{
    public partial class FrmSpaceDataExtractor : BecaBaseForm
    {
        #region Fields
        Document _doc;

        public List<string> _SelectedCategories;
        public string _NameNumber;
        public string _ParameterName;
        public string _Prefix;
        public string _Suffix;
        public string _Separator;

        public IEnumerable<View> SelectedViews { get; set; }
        public IEnumerable<Level> SelectedLevels { get; set; }
        #endregion

        #region Constructor
        public FrmSpaceDataExtractor(Document doc, SpaceRoom spaceOrRoom)
        {
            InitializeComponent();
            _doc = doc;

            if (spaceOrRoom == SpaceRoom.Room)
            {
                lblSelectedIndexRun.Text = "This tool will extract the Revit Room Name and Number and write the\r\nRoom Name and Number to a specific parameter for all elements of a \r\nspecific category within that room.";
                TitleText = "Room Data Extractor";
                label2.Text = "Extract the Room Name, Number or Both:";
                rb_SpaceNumber.Text = "Room Number";
            }


            foreach (var item in ZDE_Names.CategoryDictionary.Keys)
            {
                if (new FilteredElementCollector(_doc).OfCategory(item).OfClass(typeof(FamilyInstance)).Count() > 0)
                    clb_Categories.Items.Add(ZDE_Names.CategoryDictionary[item]);
            }
        }
        #endregion

        #region Button Click
        protected override void btnHelp_Click(object sender, EventArgs e)
        {
            using (FrmSpaceDataExtractorHelp helpFrm = new FrmSpaceDataExtractorHelp())
            {
                helpFrm.ShowDialog();
            }
        }

        private void btn_Run_Click(object sender, EventArgs e)
        {
            if (clb_Categories.CheckedItems.Count == 0)
            {
                MessageBox.Show("Please select categories.");
                DialogResult = DialogResult.None;
                return;
            }

            if (cb_ParameterNameList.SelectedItem == null)
            {
                MessageBox.Show("Please select parameter from the list.");
                DialogResult = DialogResult.None;
                return;
            }

            _SelectedCategories = clb_Categories.CheckedItems.OfType<string>().ToList();
            _NameNumber = gb_NameNum.Controls.OfType<System.Windows.Forms.RadioButton>().FirstOrDefault(r => r.Checked).Text;
            //_ParameterName = tb_ParameterName.Text;
            _ParameterName = cb_ParameterNameList.SelectedItem.ToString();
            _Prefix = tb_Prefix.Text;
            _Suffix = tb_Suffix.Text;
            _Separator = tb_Separator.Text;
        }

        private void btn_SelectAll_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clb_Categories.Items.Count; i++)
            {
                clb_Categories.SetItemChecked(i, true);
            }
        }

        private void btn_SelectNone_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clb_Categories.Items.Count; i++)
            {
                clb_Categories.SetItemChecked(i, false);
            }
        }
        #endregion

        private void cb_ParameterNameList_DropDown(object sender, EventArgs e)
        {
            cb_ParameterNameList.DataSource = null;
            if (clb_Categories.CheckedItems.Count > 0)
                cb_ParameterNameList.DataSource = BecaRevitUtilities.CategoryUtility.
                    GetCommonParameterListFromCategories(_doc, ZDE_Utility.GetSelectedCategories(clb_Categories.CheckedItems.OfType<string>().ToList()));
            else
                MessageBox.Show("Please select category");
        }

        #region View and Level filter

        #endregion

        private void btn_SelectViews_Click(object sender, EventArgs e)
        {
            using (var frmSelectViews = new FrmSelectViews(GetAvailableViews(_doc)))
            {
                frmSelectViews.ShowDialog();
                if (frmSelectViews.DialogResult == DialogResult.OK)
                {
                    SelectedViews = frmSelectViews.SelectedViews;
                    lbl_SelectedViews.Text = $"{SelectedViews.Count()} views selected";
                }
            }
        }

        private void btn_SelectLevels_Click(object sender, EventArgs e)
        {
            using (var frmSelectLevels = new FrmSelectLevels(GetAvailableLevels(_doc)))
            {
                frmSelectLevels.ShowDialog();
                if (frmSelectLevels.DialogResult == DialogResult.OK)
                {
                    SelectedLevels = frmSelectLevels.SelectedLevels;
                    lbl_SelectedLevels.Text = $"{SelectedLevels.Count()} levels selected";
                }
            }
        }

        private IEnumerable<View> GetAvailableViews(Document doc)
        {
            foreach (View view in new FilteredElementCollector(doc)
                .OfClass(typeof(View))
                .Cast<View>()
                .OrderBy(v => v.Name))
            {
                if (!view.IsTemplate)
                {
                    yield return view;
                }
            }
        }

        private IEnumerable<Level> GetAvailableLevels(Document doc)
        {
            foreach (Level level in new FilteredElementCollector(doc)
                .OfClass(typeof(Level))
                .Cast<Level>()
                .OrderBy(x => x.Elevation))
            {
                yield return level;
            }
        }

        private void cb_ByActiveView_CheckedChanged(object sender, EventArgs e)
        {
            var view = _doc.ActiveView;

            if (cb_ByActiveView.Checked)
            {
                btn_SelectViews.Enabled = false;
                SelectedViews = new List<View>() { _doc.ActiveView };
                lbl_SelectedViews.Text = "Active view selected";
            }
            else
            {
                btn_SelectViews.Enabled = true;
                SelectedViews = new List<View>();
                lbl_SelectedViews.Text = $"{SelectedViews.Count()} views selected";
            }
        }
    }
}