﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaCommand;
using BecaRevitUtilities;
using BecaTransactionsNamesManager;
using Common.UI.Forms;
using GEN.LinkedFileMapper.UI.Forms;
using MEP.SpaceDataExtractor.CoreLogic;
using MEP.SpaceDataExtractor.UI.Forms;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;

namespace MEP.SpaceDataExtractor.RevitCommands
{
    public enum SpaceRoom
    {
        Space,
        Room
    }

    [Transaction(TransactionMode.Manual)]
    class SpaceDataExtractorCommand : BecaBaseCommand
    {
        #region Field
        //public ZDE_Data data;
        //public List<string> _SelectedCategories;
        #endregion
        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            Autodesk.Revit.ApplicationServices.Application application = uiapp.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            Document localDoc = uidoc.Document;



            Document linkedDoc = null;

            _taskLogger.PreTaskStart();

            var spaceOrRoom = SpaceRoom.Space;
            int successElement;
            List<ZDE_Element> noSpaceElements;
            List<ZDE_Element_Room> noRoomElements;
            var userName = application.Username;

            #region TaskDialog Space or Room
            TaskDialog taskDialog = new TaskDialog("Space/Room Data Extractor");
            taskDialog.MainContent = "Do you want to extract Space or Room?";
            taskDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink1, "Space");
            taskDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink2, "Room");
            taskDialog.CommonButtons = TaskDialogCommonButtons.Cancel;
            TaskDialogResult dialogResult = taskDialog.Show();
            switch (dialogResult)
            {
                case TaskDialogResult.Cancel:
                    return Result.Cancelled;
                case TaskDialogResult.CommandLink1:
                    break;
                case TaskDialogResult.CommandLink2:
                    spaceOrRoom = SpaceRoom.Room;
                    linkedDoc = SelectedLinkedDocument(localDoc);
                    if (linkedDoc == null) { return Result.Failed; }
                    break;
            }
            #endregion

            var frmSpaceDataExtractor = new FrmSpaceDataExtractor(localDoc, spaceOrRoom);
            frmSpaceDataExtractor.ShowDialog();
            if (DialogResult.Cancel == frmSpaceDataExtractor.DialogResult)
            {
                return Result.Cancelled;
            }

            var selectedViewsToFilter = frmSpaceDataExtractor.SelectedViews;
            var selectedLevelsToFilter = frmSpaceDataExtractor.SelectedLevels;

            if (spaceOrRoom == SpaceRoom.Space && linkedDoc == null)
            {
                var data = new ZDE_Data(selectedLevelsToFilter, selectedViewsToFilter);
                //_SelectedCategories = frmSpaceDataExtractor._SelectedCategories;
                data.LoadAll(localDoc, ZDE_Utility.GetSelectedCategories(frmSpaceDataExtractor._SelectedCategories));

                using (Transaction trans = new Transaction(localDoc, BecaTransactionsNames.SDE_SetParameter.GetHumanReadableString()))
                {
                    trans.Start();
                    ZDE_Utility.SetZDE_ElementsParameter(data, frmSpaceDataExtractor, out successElement, out noSpaceElements);
                    trans.Commit();
                }

                StringBuilder sb = new StringBuilder();
                foreach (var item in noSpaceElements)
                {
                    sb.AppendLine(item.E.Id + ": " + item.E.Name);
                }

                TaskDialog mainDialog = new TaskDialog("Space Data Extractor Result");

                if (successElement == 0)
                {
                    mainDialog.MainContent = "The parameter which you entered does not exist,\n" +
                        "please check your spelling or ensure that the parameter is present in your project.\n\nParameter Name Entered:\n\n" + frmSpaceDataExtractor._ParameterName;

                    mainDialog.Show();
                    return Result.Failed;
                }
                else
                {
                    if (noSpaceElements.Count == 0)
                    {
                        mainDialog.MainContent = data.Elements.Count.ToString() + " elements, and " + frmSpaceDataExtractor._SelectedCategories.Count.ToString() + " categories processed.\n" +
                            successElement.ToString() + " parameters have been updated.";
                    }
                    else
                    {
                        mainDialog.MainContent = data.Elements.Count.ToString() + " elements, and " + frmSpaceDataExtractor._SelectedCategories.Count.ToString() + " categories processed.\n" +
                            successElement.ToString() + " parameters have been updated.\n\n" + noSpaceElements.Count + " elements are not part of a Space:\n\n" +
                            "You can either create a 3D view of elements not part of a\nSpace or you can export a list of elements not part of a space.";

                        mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink1, "Create 3D View");
                        mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink2, "Export List");

                        TaskDialogResult tResult = mainDialog.Show();

                        switch (tResult)
                        {
                            case TaskDialogResult.CommandLink1:
                                ZDE_Utility.Create3DView(localDoc, noSpaceElements, userName);
                                break;
                            case TaskDialogResult.CommandLink2:
                                ZDE_Utility.ExportList(noSpaceElements);
                                break;
                        }

                        mainDialog.MainContent = "!! Remember to Sync after using this tool!!";
                    }
                    TaskDialog.Show("Sync reminder.", "!! Remember to Sync after using this tool!!");
                }
            }
            else if (linkedDoc != null)
            {
                var data = new ZDE_Data_Rooms(selectedLevelsToFilter, selectedViewsToFilter);
                //_SelectedCategories = frmSpaceDataExtractor._SelectedCategories;
                data.LoadAll(localDoc, linkedDoc, ZDE_Utility.GetSelectedCategories(frmSpaceDataExtractor._SelectedCategories));

                using (Transaction trans = new Transaction(localDoc, BecaTransactionsNames.SDE_SetParameter.GetHumanReadableString()))
                {
                    trans.Start();
                    ZDE_Utility.SetZDE_ElementsParameter_Room(data, frmSpaceDataExtractor, out successElement, out noRoomElements);
                    trans.Commit();
                }

                StringBuilder sb = new StringBuilder();
                foreach (var item in noRoomElements)
                {
                    sb.AppendLine(item.E.Id + ": " + item.E.Name);
                }

                TaskDialog mainDialog = new TaskDialog("Room Data Extractor Result");

                if (successElement == 0)
                {
                    if (noRoomElements.Count > 0)
                    {
                        mainDialog.MainContent = data.Elements.Count.ToString() + " elements, and " + frmSpaceDataExtractor._SelectedCategories.Count.ToString() + " categories processed.\n" +
                            successElement.ToString() + " parameters have been updated.";
                        mainDialog.Show();
                    }
                    else
                    {
                        mainDialog.MainContent = "The parameter which you entered does not exist,\n" +
                        "please check your spelling or ensure that the parameter is present in your project.\n\nParameter Name Entered:\n\n" + frmSpaceDataExtractor._ParameterName;

                        mainDialog.Show();
                    }
                    return Result.Failed;
                }
                else
                {
                    if (noRoomElements.Count == 0)
                    {
                        mainDialog.MainContent = data.Elements.Count.ToString() + " elements, and " + frmSpaceDataExtractor._SelectedCategories.Count.ToString() + " categories processed.\n" +
                            successElement.ToString() + " parameters have been updated.";
                    }
                    else
                    {
                        mainDialog.MainContent = data.Elements.Count.ToString() + " elements, and " + frmSpaceDataExtractor._SelectedCategories.Count.ToString() + " categories processed.\n" +
                            successElement.ToString() + " parameters have been updated.\n\n" + noRoomElements.Count + " elements are not part of a Space:\n\n" +
                            "You can either create a 3D view of elements not part of a\nSpace or you can export a list of elements not part of a space.";

                        mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink1, "Create 3D View");
                        mainDialog.AddCommandLink(TaskDialogCommandLinkId.CommandLink2, "Export List");

                        TaskDialogResult tResult = mainDialog.Show();

                        switch (tResult)
                        {
                            case TaskDialogResult.CommandLink1:
                                ZDE_Utility.Create3DView_Room(localDoc, noRoomElements, userName);
                                break;
                            case TaskDialogResult.CommandLink2:
                                ZDE_Utility.ExportList_Room(noRoomElements);
                                break;
                        }

                        mainDialog.MainContent = "!! Remember to Sync after using this tool!!";
                    }
                    TaskDialog.Show("Sync reminder.", "!! Remember to Sync after using this tool!!");
                }
            }
            

            _taskLogger.PostTaskEnd("Space Data Extractor completed.");

            return Result.Succeeded;
        }

        private Document SelectedLinkedDocument(Document localDoc)
        {
            // Get link documents
            var initialLinkDocuments = LinkedFilesUtility.GetRVTLinks(localDoc);

            var frmSL = new FrmSelectLinks(initialLinkDocuments);
            frmSL.ShowDialog();

            var linkDocuments = frmSL.SelectedLinks;

            // Check links, if no links found stop 
            if (linkDocuments.Count == 0)
            {
                TaskDialog.Show("No linked File", "This model doesn't have linked file(s).");
                return null;
            }
            else
            {
                return linkDocuments[0];
            }
        }

        public override string GetAddinAuthor()
        {
            return "Firza Utama";
        }

        public override string GetAddinName()
        {
            return AddinNames.SpaceDataExtractor.Value;
        }

        public override string GetCommandSubName()
        {
            return string.Empty;
        }
    }
}
