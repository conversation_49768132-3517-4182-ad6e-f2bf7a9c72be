﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SpaceDataExtractor.CoreLogic
{
    public class ZDE_Element_Room
    {
        #region Fields
        public Element E { get; }
        public string Level { get; }
        public XYZ[] Coordinates { get; }

        public ZDE_Room HostRoom;
        #endregion

        #region Constructor
        public ZDE_Element_Room(Document doc, Element e)
        {
            this.E = e;
            Level = ZDE_Utility.AssignLevel(doc, e);
            Coordinates = ZDE_Utility.ElementCoordinates(doc, e);

        }
        #endregion

        #region Method
        public void AssignHostRoom(ZDE_Room r)
        {
            HostRoom = r;
        }
        #endregion
    }
}
