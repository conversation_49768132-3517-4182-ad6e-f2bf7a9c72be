﻿namespace MEP.SpaceDataExtractor.UI.Forms
{
    partial class FrmSelectViews
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            clb_Views = new CheckedListBox();
            btn_Back = new Button();
            lbl_SelectedViews = new Label();
            tableLayoutPanel1 = new TableLayoutPanel();
            groupBox1 = new GroupBox();
            btn_SelectNone = new Button();
            btn_SelectAll = new Button();
            txt_Search = new TextBox();
            tableLayoutPanel1.SuspendLayout();
            groupBox1.SuspendLayout();
            SuspendLayout();
            // 
            // clb_Views
            // 
            clb_Views.CheckOnClick = true;
            clb_Views.Dock = DockStyle.Fill;
            clb_Views.FormattingEnabled = true;
            clb_Views.Location = new Point(3, 113);
            clb_Views.Name = "clb_Views";
            clb_Views.Size = new Size(382, 323);
            clb_Views.TabIndex = 3;
            clb_Views.ItemCheck += clb_Views_ItemCheck;
            // 
            // btn_Back
            // 
            btn_Back.Anchor = AnchorStyles.Right;
            btn_Back.BackColor = Color.FromArgb(18, 168, 178);
            btn_Back.FlatAppearance.BorderColor = Color.FromArgb(18, 168, 178);
            btn_Back.FlatStyle = FlatStyle.Flat;
            btn_Back.Font = new Font("Arial", 9F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btn_Back.ForeColor = Color.White;
            btn_Back.Location = new Point(274, 446);
            btn_Back.Margin = new Padding(2, 3, 20, 3);
            btn_Back.Name = "btn_Back";
            btn_Back.Size = new Size(94, 33);
            btn_Back.TabIndex = 152;
            btn_Back.Text = "Back";
            btn_Back.UseVisualStyleBackColor = false;
            btn_Back.Click += btn_Back_Click;
            // 
            // lbl_SelectedViews
            // 
            lbl_SelectedViews.Anchor = AnchorStyles.Left;
            lbl_SelectedViews.AutoSize = true;
            lbl_SelectedViews.ForeColor = Color.FromArgb(18, 168, 178);
            lbl_SelectedViews.Location = new Point(3, 6);
            lbl_SelectedViews.Name = "lbl_SelectedViews";
            lbl_SelectedViews.Size = new Size(86, 15);
            lbl_SelectedViews.TabIndex = 153;
            lbl_SelectedViews.Text = "0 view selected";
            // 
            // tableLayoutPanel1
            // 
            tableLayoutPanel1.ColumnCount = 1;
            tableLayoutPanel1.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.Controls.Add(groupBox1, 0, 1);
            tableLayoutPanel1.Controls.Add(lbl_SelectedViews, 0, 0);
            tableLayoutPanel1.Controls.Add(clb_Views, 0, 2);
            tableLayoutPanel1.Controls.Add(btn_Back, 0, 3);
            tableLayoutPanel1.Dock = DockStyle.Fill;
            tableLayoutPanel1.Location = new Point(0, 46);
            tableLayoutPanel1.Name = "tableLayoutPanel1";
            tableLayoutPanel1.RowCount = 4;
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 27F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 83F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 47F));
            tableLayoutPanel1.RowStyles.Add(new RowStyle(SizeType.Absolute, 20F));
            tableLayoutPanel1.Size = new Size(388, 486);
            tableLayoutPanel1.TabIndex = 154;
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(btn_SelectNone);
            groupBox1.Controls.Add(btn_SelectAll);
            groupBox1.Controls.Add(txt_Search);
            groupBox1.Dock = DockStyle.Fill;
            groupBox1.Location = new Point(3, 30);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(382, 77);
            groupBox1.TabIndex = 157;
            groupBox1.TabStop = false;
            groupBox1.Text = "Search View";
            // 
            // btn_SelectNone
            // 
            btn_SelectNone.Anchor = AnchorStyles.Left;
            btn_SelectNone.BackColor = Color.FromArgb(18, 168, 178);
            btn_SelectNone.FlatAppearance.BorderColor = Color.FromArgb(18, 168, 178);
            btn_SelectNone.FlatStyle = FlatStyle.Flat;
            btn_SelectNone.Font = new Font("Arial", 9F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btn_SelectNone.ForeColor = Color.White;
            btn_SelectNone.Location = new Point(99, 46);
            btn_SelectNone.Margin = new Padding(2, 3, 20, 3);
            btn_SelectNone.Name = "btn_SelectNone";
            btn_SelectNone.Size = new Size(87, 25);
            btn_SelectNone.TabIndex = 159;
            btn_SelectNone.Text = "Select None";
            btn_SelectNone.UseVisualStyleBackColor = false;
            btn_SelectNone.Click += btn_SelectNone_Click;
            // 
            // btn_SelectAll
            // 
            btn_SelectAll.Anchor = AnchorStyles.Left;
            btn_SelectAll.BackColor = Color.FromArgb(18, 168, 178);
            btn_SelectAll.FlatAppearance.BorderColor = Color.FromArgb(18, 168, 178);
            btn_SelectAll.FlatStyle = FlatStyle.Flat;
            btn_SelectAll.Font = new Font("Arial", 9F, FontStyle.Regular, GraphicsUnit.Point, 0);
            btn_SelectAll.ForeColor = Color.White;
            btn_SelectAll.Location = new Point(8, 46);
            btn_SelectAll.Margin = new Padding(2, 3, 20, 3);
            btn_SelectAll.Name = "btn_SelectAll";
            btn_SelectAll.Size = new Size(87, 25);
            btn_SelectAll.TabIndex = 158;
            btn_SelectAll.Text = "Select All";
            btn_SelectAll.UseVisualStyleBackColor = false;
            btn_SelectAll.Click += btn_SelectAll_Click;
            // 
            // txt_Search
            // 
            txt_Search.Anchor = AnchorStyles.Left;
            txt_Search.Location = new Point(8, 17);
            txt_Search.Name = "txt_Search";
            txt_Search.Size = new Size(249, 23);
            txt_Search.TabIndex = 154;
            txt_Search.TextChanged += txt_Search_TextChanged;
            // 
            // FrmSelectViews
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(388, 591);
            Controls.Add(tableLayoutPanel1);
            Name = "FrmSelectViews";
            Text = "FrmSelectViews";
            TitleText = "SELECT VIEWS";
            Controls.SetChildIndex(tableLayoutPanel1, 0);
            tableLayoutPanel1.ResumeLayout(false);
            tableLayoutPanel1.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ResumeLayout(false);
        }

        #endregion

        private CheckedListBox clb_Views;
        private Button btn_Back;
        private Label lbl_SelectedViews;
        private TableLayoutPanel tableLayoutPanel1;
        private TextBox txt_Search;
        private GroupBox groupBox1;
        private Button btn_SelectNone;
        private Button btn_SelectAll;
    }
}