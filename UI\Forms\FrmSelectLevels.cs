﻿using Autodesk.Revit.DB;
using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace MEP.SpaceDataExtractor.UI.Forms
{
    public partial class FrmSelectLevels : BecaBaseForm
    {
        private IEnumerable<Level> _availableLevels;
        private IEnumerable<Level> _filteredLevels;
        private Dictionary<string, Level> _levelDictionary;

        public IEnumerable<Level> SelectedLevels { get; set; }

        public FrmSelectLevels(IEnumerable<Level> availableLevels)
        {
            InitializeComponent();
            _availableLevels = availableLevels;
            _filteredLevels = availableLevels;

            LoadAvailableLevels();
        }

        private void LoadAvailableLevels()
        {
            clb_Levels.Items.Clear();

            _levelDictionary = new Dictionary<string, Level>();

            foreach (Level level in _filteredLevels)
            {
                clb_Levels.Items.Add(level.Name, false);
                _levelDictionary[level.Name] = level;
            }
        }

        private void clb_Levels_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            BeginInvoke(new Action(UpdateCheckedCount));
        }

        private void UpdateCheckedCount()
        {
            int checkedCount = clb_Levels.CheckedItems.Count;
            lbl_SelectedLevels.Text = $"{checkedCount} levels selected";
        }

        private void txt_Search_TextChanged(object sender, EventArgs e)
        {
            string searchTerm = txt_Search.Text.ToLower();

            // Filter views based on the search query
            if (string.IsNullOrEmpty(searchTerm))
            {
                _filteredLevels = _availableLevels;
            }
            else
            {
                _filteredLevels = _availableLevels.Where(v => v.Name.ToLower().Contains(searchTerm));
            }


            // Reload the CheckedListBox with filtered views
            LoadAvailableLevels();
        }

        private void btn_SelectNone_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clb_Levels.Items.Count; i++)
            {
                clb_Levels.SetItemChecked(i, false);
            }
        }

        private void btn_SelectAll_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clb_Levels.Items.Count; i++)
            {
                clb_Levels.SetItemChecked(i, true);
            }
        }

        private void btn_Back_Click(object sender, EventArgs e)
        {
            var selectedLevels = new List<Level>();

            foreach (var checkedItem in clb_Levels.CheckedItems)
            {
                if (checkedItem is string levelName && _levelDictionary.ContainsKey(levelName))
                {
                    selectedLevels.Add(_levelDictionary[levelName]);
                }
            }

            SelectedLevels = selectedLevels;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }
    }
}
