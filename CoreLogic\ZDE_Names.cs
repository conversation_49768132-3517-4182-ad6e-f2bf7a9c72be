﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SpaceDataExtractor.CoreLogic
{
    public static class ZDE_Names
    {
        public static readonly Dictionary<BuiltInCategory, string> CategoryDictionary = new Dictionary<BuiltInCategory, string>
        {
            { BuiltInCategory.OST_DuctTerminal, "Air Terminals" },
            { BuiltInCategory.OST_CommunicationDevices, "Communication Devices" },
            { BuiltInCategory.OST_DataDevices, "Data Devices" },
            { BuiltInCategory.OST_DuctAccessory, "Duct Accessories" },
            { BuiltInCategory.OST_ElectricalEquipment, "Electrical Equipment" },
            { BuiltInCategory.OST_ElectricalFixtures, "Electrical Fixtures" },
            { BuiltInCategory.OST_FireAlarmDevices, "Fire Alarm Devices" },
            { BuiltInCategory.OST_LightingDevices, "Lighting Devices" },
            { BuiltInCategory.OST_LightingFixtures, "Lighting Fixtures" },
            { BuiltInCategory.OST_MechanicalEquipment, "Mechanical Equipment" },
            { BuiltInCategory.OST_NurseCallDevices, "Nurse Call Devices" },
            { BuiltInCategory.OST_PipeAccessory, "Pipe Accessories" },
            { BuiltInCategory.OST_PlumbingFixtures, "Plumbing Fixtures" },
            { BuiltInCategory.OST_SecurityDevices, "Security Devices" },
            { BuiltInCategory.OST_Sprinklers, "Sprinklers" },
            { BuiltInCategory.OST_GenericModel, "Generic Models" },
            { BuiltInCategory.OST_TelephoneDevices, "Telephone Devices" }
        };

    }
}
