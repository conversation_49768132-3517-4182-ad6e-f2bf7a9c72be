﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.DB.Mechanical;
using BecaRevitUtilities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;

namespace MEP.SpaceDataExtractor.CoreLogic
{
    public class ZDE_Data
    {
        #region Fields
        private bool _elementsLoaded;
        private bool _elementsAssigned;
        public List<ZDE_Element> Elements;

        private bool _spacesLoaded;
        public List<ZDE_Space> Spaces;

        private IEnumerable<Level> _levelsToFilter;
        private IEnumerable<View> _viewsToFilter;
        #endregion

        #region Constructor
        public ZDE_Data(IEnumerable<Level> levelsToFilter, IEnumerable<View> viewsToFilter)
        {
            Elements = new List<ZDE_Element>();

            Spaces = new List<ZDE_Space>();
            _spacesLoaded = false;

            _elementsAssigned = false;

            _levelsToFilter = levelsToFilter;
            _viewsToFilter = viewsToFilter;
        }
        #endregion

        #region Methods
        public void LoadAll(Document doc, List<BuiltInCategory> bics)
        {
            LoadSpaces(doc);

            LoadElements(doc, bics);

            AssignElements();

            return;
        }

        private void AssignElements()
        {
            ZDE_Space s;

            if (_elementsAssigned)
                return;

            if (!_elementsLoaded || !_spacesLoaded)
            {
                return;
            }

            foreach (var item in Spaces)
            {
                item.ClearElementss();
            }

            foreach (var item in Elements)
            {
                if ((s = FindElementsSpaces(item)) != null)
                {
                    s.AddElement(item);
                }
            }

            _elementsAssigned = true;
        }

        private ZDE_Space FindElementsSpaces(ZDE_Element e)
        {
            for (int i = 0; i < 7; i++)
            {
                foreach (var item in Spaces)
                {
                    if (item.SpcSpace.IsPointInSpace(e.Coordinates[i]) && item.SpcSpace.Area >= 1)
                    {
                        e.AssignHostSpace(item);
                        return item;
                    }
                }
            }
            return null;
        }

        private void LoadElements(Document doc, List<BuiltInCategory> bics)
        {
            if (_elementsLoaded)
                return;

            Elements.Clear();

            foreach (var item in bics)
            {
                List<Element> filteredElements = ZDE_Utility.GetFilteredElements(_levelsToFilter, _viewsToFilter, doc, item);

                foreach (var e in filteredElements)
                {
                    ZDE_Element element = new ZDE_Element(doc, e);
                    Elements.Add(element);
                }
            }
            
            Elements = Elements.OrderBy(x => x.Level).ToList();

            _elementsLoaded = true;
        }

        private void LoadSpaces(Document doc)
        {
            const double ft2m = 0.3048;

            if (_spacesLoaded)
                return;

            Spaces.Clear();

            var spaces = new FilteredElementCollector(doc).OfCategory(BuiltInCategory.OST_MEPSpaces).Cast<Space>().Where(s => RevitUnitConvertor.InternalToSquareMeters(s.Area) > 0);

            // Filter by level and view if any
            spaces = ZDE_Utility.FilterSpacesByLevels(_levelsToFilter, spaces);
            spaces = ZDE_Utility.FilterSpacesByViews(_viewsToFilter, spaces);

            foreach ( var s in spaces)
            {
                if (s.Area >= 1 * ft2m * ft2m)
                {
                    ZDE_Space b = new ZDE_Space(doc, s);
                    Spaces.Add(b);
                }
            }

            Spaces = Spaces.OrderBy(x => x.Level).ThenBy(x => x.Num).ToList();

            _spacesLoaded = true;
        }
        #endregion
    }
}
