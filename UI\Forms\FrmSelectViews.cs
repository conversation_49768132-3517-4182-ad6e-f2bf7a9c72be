﻿using Common.UI.Forms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Autodesk.Revit.DB;
using View = Autodesk.Revit.DB.View;

namespace MEP.SpaceDataExtractor.UI.Forms
{
    public partial class FrmSelectViews : BecaBaseForm
    {
        private IEnumerable<View> _availableViews;
        private IEnumerable<View> _filteredViews;
        private Dictionary<string, View> _viewDictionary;

        public IEnumerable<View> SelectedViews { get; set; }

        public FrmSelectViews(IEnumerable<View> availableViews)
        {
            InitializeComponent();
            _availableViews = availableViews;
            _filteredViews = availableViews;

            LoadAvailableViews();
        }

        private void LoadAvailableViews()
        {
            clb_Views.Items.Clear();

            _viewDictionary = new Dictionary<string, View>();

            foreach (var view in _filteredViews)
            {
                clb_Views.Items.Add(view.Name, false);
                _viewDictionary[view.Name] = view;
            }
        }

        private void txt_Search_TextChanged(object sender, EventArgs e)
        {
            string searchTerm = txt_Search.Text.ToLower();

            // Filter views based on the search query
            if (string.IsNullOrEmpty(searchTerm))
            {
                _filteredViews = _availableViews;
            }
            else
            {
                _filteredViews = _availableViews.Where(v => v.Name.ToLower().Contains(searchTerm));
            }

            // Reload the CheckedListBox with filtered views
            LoadAvailableViews();
        }

        private void clb_Views_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            BeginInvoke(new Action(UpdateCheckedCount));
        }

        private void UpdateCheckedCount()
        {
            int checkedCount = clb_Views.CheckedItems.Count;
            lbl_SelectedViews.Text = $"{checkedCount} views selected";
        }

        private void btn_Back_Click(object sender, EventArgs e)
        {
            var selectedViews = new List<View>();

            foreach (var checkedItem in clb_Views.CheckedItems)
            {
                if (checkedItem is string viewName && _viewDictionary.ContainsKey(viewName))
                {
                    selectedViews.Add(_viewDictionary[viewName]);
                }
            }

            SelectedViews = selectedViews;
            this.DialogResult = DialogResult.OK;
            this.Close();
        }

        private void btn_SelectAll_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clb_Views.Items.Count; i++)
            {
                clb_Views.SetItemChecked(i, true);
            }
        }

        private void btn_SelectNone_Click(object sender, EventArgs e)
        {
            for (int i = 0; i < clb_Views.Items.Count; i++)
            {
                clb_Views.SetItemChecked(i, false);
            }
        }
    }
}
