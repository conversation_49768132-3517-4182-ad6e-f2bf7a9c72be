﻿using Autodesk.Revit.DB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SpaceDataExtractor.CoreLogic
{
    public class ZDE_Element
    {
        #region Fields
        public Element E { get; }
        public string Level { get; }
        public XYZ[] Coordinates { get; }

        public ZDE_Space HostSpace;
        #endregion

        #region Constructor
        public ZDE_Element(Document doc, Element e)
        {
            this.E = e;
            Level = ZDE_Utility.AssignLevel(doc, e);
            Coordinates = ZDE_Utility.ElementCoordinates(doc, e);

        }
        #endregion

        #region Method
        public void AssignHostSpace(ZDE_Space s)
        {
            HostSpace = s;
        }
        #endregion
    }
}
