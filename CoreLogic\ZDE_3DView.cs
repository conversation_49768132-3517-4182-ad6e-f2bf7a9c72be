﻿using Autodesk.Revit.DB;
using BecaRevitUtilities;
using BecaRevitUtilities.RevitViewsUtilities;
using BecaTransactionsNamesManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using View = Autodesk.Revit.DB.View;


namespace MEP.SpaceDataExtractor.CoreLogic
{
    public static class ZDE_3DView
    {
        public static bool CreateViews(Document doc, List<ElementId> notConnectedIds, string ColourOverride, string Isolate)
        {
            bool result = false;

            //Override graphics element
            OverrideGraphicSettings ogs_ElementNotConnected, ogs_ElementConnected;
            GraphicOverrideUtility.GetOverrideGraphicSettings(doc, out ogs_ElementConnected, out ogs_ElementNotConnected);

            using (Transaction t = new Transaction(doc, BecaTransactionsNames.overrideElementConnection.GetHumanReadableString()))
            {
                t.Start();

                //Handling Isolation
                if (ViewUtility.ViewNameExist(doc, Isolate))
                {
                    doc.Delete(Get3DView(doc, Isolate).Id);
                    CreateConnectionView(doc, Isolate).IsolateElementsTemporary(notConnectedIds);
                }
                else
                {
                    Get3DView(doc, Isolate).IsolateElementsTemporary(notConnectedIds);
                }

                //Handling Override Graphics
                View3D colourOverrideView = Get3DView(doc, ColourOverride);
                var allElementsInView = new FilteredElementCollector(doc, colourOverrideView.Id);
                FilteredElementIdIterator allElementsIDsInView = allElementsInView.GetElementIdIterator();
                allElementsIDsInView.Reset();

                while (allElementsIDsInView.MoveNext())
                {
                    if (notConnectedIds.Contains(allElementsIDsInView.Current))
                        colourOverrideView.SetElementOverrides(allElementsIDsInView.Current, ogs_ElementNotConnected);
                    else
                        colourOverrideView.SetElementOverrides(allElementsIDsInView.Current, ogs_ElementConnected);
                }

                if (t.Commit() == TransactionStatus.Committed)
                    result = true;
                else result = false;
            }

            return result;
        }

        static View3D Get3DView(Document doc, string view3DName)
        {
            if (doc == null || doc.IsFamilyDocument)
                return null;


            var views3D = new FilteredElementCollector(doc)
                .OfClass(typeof(View3D)).Cast<View3D>();
            var existedView = views3D.FirstOrDefault(view => !view.IsTemplate &&
            view.Name.Equals(view3DName));
            if (existedView != null)
                return existedView;
            else
                return CreateConnectionView(doc, view3DName);
        }

        static View3D CreateConnectionView(Document Doc, string view3DName)
        {
            View3D view3D = null;
            var direction = new XYZ(-1, 1, -1);
            var collector = new FilteredElementCollector(Doc);
            var viewFamilyType = collector.OfClass(typeof(ViewFamilyType)).Cast<ViewFamilyType>()
              .FirstOrDefault(x => x.ViewFamily == ViewFamily.ThreeDimensional);

            view3D = View3D.CreateIsometric(Doc, viewFamilyType.Id);
            view3D.Name = view3DName;
            view3D.SetOrientation(new ViewOrientation3D(
              direction, new XYZ(0, 1, 1), new XYZ(0, 1, -1)));
            view3D.Scale = 100;
            view3D.DetailLevel = ViewDetailLevel.Fine;
            HideAllLinks(view3D);

            return view3D;
        }

        public static void HideAllLinks(View revitView)
        {
            FilteredElementCollector collector = new FilteredElementCollector(revitView.Document);
            ICollection<ElementId> elementIdSet =
              collector
              .OfCategory(BuiltInCategory.OST_RvtLinks)
              .OfClass(typeof(RevitLinkType))
              .ToElementIds();
            if (elementIdSet.Any())
            {
                revitView.HideElements(elementIdSet);
            }
        }
    }
}
