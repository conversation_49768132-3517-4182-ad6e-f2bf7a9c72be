﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SpaceDataExtractor.CoreLogic
{
    public class ZDE_Space
    {
        #region Field
        public Space SpcSpace { get; set; }
        public string Level { get; set; }
        public string Num { get; set; }
        public string Name { get; set; }
        public int ElementCount { get; set; }

        private List<ZDE_Element> _elements;
        #endregion

        #region Constructor
        public ZDE_Space(Document doc, Space s)
        {
            // Populating the basic values.
            SpcSpace = s;
            Name = s.Name.Replace(" " + s.Number.ToString(), "");
            Level = s.Level.Name;
            Num = s.Number;
            ElementCount = 0;

            // Creating the list for data devices and electrical fixtures.
            _elements = new List<ZDE_Element>();
        }
        #endregion

        #region Methods
        public void ClearElementss()
        {
            if (_elements.Count > 0)
                _elements.Clear();
            return;
        }

        public void AddElement(ZDE_Element electricalFixture)
        {
            ElementCount++;
            _elements.Add(electricalFixture);

            return;
        }
        #endregion
    }
}
