﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.DB.Mechanical;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.SpaceDataExtractor.CoreLogic
{
    public class ZDE_Room
    {
        #region Field
        public Room SpcRoom { get; set; }
        public string Level { get; set; }
        public string Num { get; set; }
        public string Name { get; set; }
        public int ElementCount { get; set; }

        private List<ZDE_Element_Room> _elements;
        #endregion

        #region Constructor
        public ZDE_Room(Room r)
        {
            // Populating the basic values.
            SpcRoom = r;
            Name = r.Name.Replace(" " + r.Number.ToString(), "");
            Level = r.Level.Name;
            Num = r.Number;
            ElementCount = 0;

            // Creating the list for data devices and electrical fixtures.
            _elements = new List<ZDE_Element_Room>();
        }
        #endregion

        #region Methods
        public void ClearElements()
        {
            if (_elements.Count > 0)
                _elements.Clear();
            return;
        }

        public void AddElement(ZDE_Element_Room element)
        {
            ElementCount++;
            _elements.Add(element);

            return;
        }
        #endregion
    }
}
