﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Architecture;
using Autodesk.Revit.DB.Mechanical;
using BecaRevitUtilities;
using Common.UI.Forms;
using MEP.SpaceDataExtractor.UI.Forms;
using System.Collections.Generic;
using TaskDialog = Autodesk.Revit.UI.TaskDialog;
using View = Autodesk.Revit.DB.View;

namespace MEP.SpaceDataExtractor.CoreLogic
{
    public static class ZDE_Utility
    {
        //public const double ft2m = 0.3048;
        public const double ft2m = UnitConversionUtility.FeetToMm;

        #region Methods
        public static string AssignLevel(Document doc, Element e)
        {
            if (doc.GetElement(e.LevelId) != null)
                return doc.GetElement(e.LevelId).Name;
            else
                return "<Not Assigned>";
        }

        public static XYZ[] ElementCoordinates(Document doc, Element e)
        {
            BoundingBoxXYZ bb = e.get_BoundingBox(null);

            XYZ[] coordinates = new XYZ[7];
            coordinates[0] = new XYZ((bb.Min.X + bb.Max.X) / 2, (bb.Min.Y + bb.Max.Y) / 2, (bb.Min.Z + bb.Max.Z) / 2);  // Centre.
            coordinates[1] = new XYZ((bb.Min.X + bb.Max.X) / 2, (bb.Min.Y + bb.Max.Y) / 2, bb.Min.Z - 0.01 / ft2m);  // Centre of z-.
            coordinates[2] = new XYZ(bb.Max.X + 0.01 / ft2m, (bb.Min.Y + bb.Max.Y) / 2, (bb.Min.Z + bb.Max.Z) / 2);  // Centre of x+.
            coordinates[3] = new XYZ(bb.Min.X - 0.01 / ft2m, (bb.Min.Y + bb.Max.Y) / 2, (bb.Min.Z + bb.Max.Z) / 2);  // Centre of x-.
            coordinates[4] = new XYZ((bb.Min.X + bb.Max.X) / 2, bb.Max.Y + 0.01 / ft2m, (bb.Min.Z + bb.Max.Z) / 2);  // Centre of y+.
            coordinates[5] = new XYZ((bb.Min.X + bb.Max.X) / 2, bb.Min.Y - 0.01 / ft2m, (bb.Min.Z + bb.Max.Z) / 2);  // Centre of y-.
            coordinates[6] = new XYZ((bb.Min.X + bb.Max.X) / 2, (bb.Min.Y + bb.Max.Y) / 2, bb.Max.Z + 0.01 / ft2m);  // Centre of z+.

            return coordinates;
        }

        public static List<BuiltInCategory> GetSelectedCategories(List<string> categoryNames)
        {
            List<BuiltInCategory> bics = new List<BuiltInCategory>();
            foreach (var item in categoryNames)
            {
                bics.Add(ZDE_Names.CategoryDictionary.FirstOrDefault(x => x.Value == item).Key);
            }
            return bics;
        }

        public static bool SetParameter(Parameter param, string nameNumber)
        {
            if (param != null)
            {
                param.Set(nameNumber);
                return true;
            }
            else { return false; }

        }

        public static void SetZDE_ElementsParameter(ZDE_Data data, FrmSpaceDataExtractor form, out int successElement, out List<ZDE_Element> noSpaceElements)
        {
            successElement = 0;
            noSpaceElements = new List<ZDE_Element>();
            //var noSpaceElements = new List<ZDE_Element>();

            int nCount = data.Elements.Count;
            string progressMessage = "{0} of " + nCount.ToString() + " elements processed...";
            string caption = "Setting Space data to the following Parameter: " + form._ParameterName;
            using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, nCount))
            {
                foreach (var item in data.Elements)
                {
                    var parameter = item.E.LookupParameter(form._ParameterName);
                    if (item.HostSpace != null)
                    {
                        switch (form._NameNumber)
                        {
                            case "Name":
                                if (ZDE_Utility.SetParameter(parameter, form._Prefix + item.HostSpace.Name + form._Suffix))
                                    successElement++;
                                break;
                            case "Space Number":
                                if (ZDE_Utility.SetParameter(parameter, form._Prefix + item.HostSpace.Num + form._Suffix))
                                    successElement++;
                                break;
                            case "Both":
                                if (ZDE_Utility.SetParameter(parameter, form._Prefix + item.HostSpace.Num + form._Separator
                                    + item.HostSpace.Name + form._Suffix))
                                    successElement++;
                                break;
                            default:
                                break;
                        }
                    }
                    else
                    {
                        noSpaceElements.Add(item);
                    }
                    pf.Increment();
                }
            }
            
        }

        public static void SetZDE_ElementsParameter_Room(ZDE_Data_Rooms data, FrmSpaceDataExtractor form, out int successElement, out List<ZDE_Element_Room> noRoomElements)
        {
            successElement = 0;
            noRoomElements = new List<ZDE_Element_Room>();
            //var noSpaceElements = new List<ZDE_Element>();

            int nCount = data.Elements.Count;
            string progressMessage = "{0} of " + nCount.ToString() + " elements processed...";
            string caption = "Setting Room data to the following Parameter: " + form._ParameterName;
            using (BecaProgressForm pf = new BecaProgressForm(caption, progressMessage, nCount))
            {
                foreach (var item in data.Elements)
                {
                    var parameter = item.E.LookupParameter(form._ParameterName);
                    if (item.HostRoom != null)
                    {
                        switch (form._NameNumber)
                        {
                            case "Name":
                                if (ZDE_Utility.SetParameter(parameter, form._Prefix + item.HostRoom.Name + form._Suffix))
                                    successElement++;
                                break;
                            case "Room Number":
                                if (ZDE_Utility.SetParameter(parameter, form._Prefix + item.HostRoom.Num + form._Suffix))
                                    successElement++;
                                break;
                            case "Both":
                                if (ZDE_Utility.SetParameter(parameter, form._Prefix + item.HostRoom.Num + form._Separator
                                    + item.HostRoom.Name + form._Suffix))
                                    successElement++;
                                break;
                            default:
                                break;
                        }
                    }
                    else
                    {
                        noRoomElements.Add(item);
                    }
                    pf.Increment();
                }
            }

        }

        public static void Create3DView(Document doc, List<ZDE_Element> noSpaceElements, string userName)
        {
            string connectionCheckViewNameColourOverride = "SpaceDataExtractor_ColourOverride_" + userName;
            string connectionCheckViewNameIsolate = "SpaceDataExtractor_Isolate_" + userName;

            var noSpaceElementIds = noSpaceElements.Select(x => x.E.Id).ToList();

            if (ZDE_3DView.CreateViews(doc, noSpaceElementIds, connectionCheckViewNameColourOverride, connectionCheckViewNameIsolate))
            {
                TaskDialog.Show("View created", "View name: " + "\n\n" + connectionCheckViewNameColourOverride + "\n\n" + connectionCheckViewNameIsolate + "\n\n" + "has been created.");
            }
            else
            {
                TaskDialog.Show("View not created", "View creation failed.");
            }
        }

        public static void Create3DView_Room(Document doc, List<ZDE_Element_Room> noRoomElements, string userName)
        {
            string connectionCheckViewNameColourOverride = "SpaceDataExtractor_ColourOverride_" + userName;
            string connectionCheckViewNameIsolate = "SpaceDataExtractor_Isolate_" + userName;

            var noRoomElementIds = noRoomElements.Select(x => x.E.Id).ToList();

            if (ZDE_3DView.CreateViews(doc, noRoomElementIds, connectionCheckViewNameColourOverride, connectionCheckViewNameIsolate))
            {
                TaskDialog.Show("View created", "View name: " + "\n\n" + connectionCheckViewNameColourOverride + "\n\n" + connectionCheckViewNameIsolate + "\n\n" + "has been created.");
            }
            else
            {
                TaskDialog.Show("View not created", "View creation failed.");
            }
        }

        public static void ExportList(List<ZDE_Element> elements)
        {
            IEnumerable<string> idName = elements.Select(x => x.E.Id.ToString() + "," + x.E.Name).ToList();
            using (var saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                saveFileDialog.Title = "Save zone data";
                saveFileDialog.DefaultExt = "csv";
                saveFileDialog.Filter = "csv files (*.csv)|*.csv|All files (*.*)|*.*";
                saveFileDialog.FilterIndex = 2;
                saveFileDialog.RestoreDirectory = true;
                saveFileDialog.FileName = "SpaceDataExtraction" + DateTime.Now.ToString(" dd_MM_yyyy_HHmmss");


                if (saveFileDialog.ShowDialog() == DialogResult.OK
                    && !string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    int nCount = elements.Count;
                    string progressMessage = "{0} of " + nCount.ToString() + " data processed...";
                    string caption = "Writing data to csv file";

                    File.AppendAllLines(saveFileDialog.FileName, idName);
                }
                else SetStatus("Exporting Excel file was canceled ...");
            }

            void SetStatus(string statusString)
            {
                MessageBox.Show("[" + DateTime.Now.ToString() + "] "
                    + statusString + Environment.NewLine);
            }
        }

        public static void ExportList_Room(List<ZDE_Element_Room> elements)
        {
            IEnumerable<string> idName = elements.Select(x => x.E.Id.ToString() + "," + x.E.Name).ToList();
            using (var saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                saveFileDialog.Title = "Save zone data";
                saveFileDialog.DefaultExt = "csv";
                saveFileDialog.Filter = "csv files (*.csv)|*.csv|All files (*.*)|*.*";
                saveFileDialog.FilterIndex = 2;
                saveFileDialog.RestoreDirectory = true;
                saveFileDialog.FileName = "SpaceDataExtraction" + DateTime.Now.ToString(" dd_MM_yyyy_HHmmss");


                if (saveFileDialog.ShowDialog() == DialogResult.OK
                    && !string.IsNullOrEmpty(saveFileDialog.FileName))
                {
                    int nCount = elements.Count;
                    string progressMessage = "{0} of " + nCount.ToString() + " data processed...";
                    string caption = "Writing data to csv file";

                    File.AppendAllLines(saveFileDialog.FileName, idName);
                }
                else SetStatus("Exporting Excel file was canceled ...");
            }

            void SetStatus(string statusString)
            {
                MessageBox.Show("[" + DateTime.Now.ToString() + "] "
                    + statusString + Environment.NewLine);
            }
        }
        #endregion

        public static List<Element> GetFilteredElements(IEnumerable<Level> levelsToFilter, IEnumerable<View> viewsToFilter, Document localDoc, BuiltInCategory item)
        {
            var categoryCollector = new FilteredElementCollector(localDoc).OfCategory(item).WhereElementIsNotElementType();

            var elements = new List<Element>();

            // Filter by level and view if any
            if (levelsToFilter != null || levelsToFilter?.Count() > 0)
            {
                var filteredElements = FilterElementsByLevels(levelsToFilter, categoryCollector.ToElements());
                foreach (var e in filteredElements)
                {
                    elements.Add(e);
                }
            }

            if (viewsToFilter != null || viewsToFilter?.Count() > 0)
            {
                foreach (var v in viewsToFilter)
                {
                    var filteredElements = new FilteredElementCollector(localDoc, v.Id).OfCategory(item).WhereElementIsNotElementType().ToElements();
                    foreach (var e in filteredElements)
                    {
                        elements.Add(e);
                    }
                }
            }

            return elements;
        }

        public static IEnumerable<Room> FilterRoomsByViews(IEnumerable<View> viewsToFilter, IEnumerable<Room> rooms)
        {
            var _rooms = new List<Room>();

            if (viewsToFilter != null && viewsToFilter.Any())
            {
                var boundingBoxFilters = new List<ElementFilter>();

                // Create BoundingBox filters for each view's bounding box
                foreach (var view in viewsToFilter)
                {
                    var boundingBox = view.get_BoundingBox(view); 
                    if (boundingBox != null)
                    {
                        var outline = new Outline(boundingBox.Min, boundingBox.Max);
                        boundingBoxFilters.Add(new BoundingBoxIntersectsFilter(outline));
                    }
                }

                if (boundingBoxFilters.Any())
                {
                    // Combine all bounding box filters into one LogicalOrFilter
                    var combinedBoundingBoxFilter = new LogicalOrFilter(boundingBoxFilters.Cast<ElementFilter>().ToList());

                    foreach (var r in rooms)
                    {
                        if (combinedBoundingBoxFilter.PassesFilter(r))
                        {
                            _rooms.Add(r);
                        }
                    }

                    if (_rooms.Count > 0)
                    {
                        return _rooms;
                    }
                }
            }

            return rooms;
        }

        public static IEnumerable<Space> FilterSpacesByViews(IEnumerable<View> viewsToFilter, IEnumerable<Space> spaces)
        {
            var _spaces = new List<Space>();

            if (viewsToFilter != null && viewsToFilter.Any())
            {
                var boundingBoxFilters = new List<ElementFilter>();

                // Create BoundingBox filters for each view's bounding box
                foreach (var view in viewsToFilter)
                {
                    var boundingBox = view.get_BoundingBox(view);
                    if (boundingBox != null)
                    {
                        var outline = new Outline(boundingBox.Min, boundingBox.Max);
                        boundingBoxFilters.Add(new BoundingBoxIntersectsFilter(outline));
                    }
                }

                if (boundingBoxFilters.Any())
                {
                    // Combine all bounding box filters into one LogicalOrFilter
                    var combinedBoundingBoxFilter = new LogicalOrFilter(boundingBoxFilters.Cast<ElementFilter>().ToList());

                    foreach (var s in spaces)
                    {
                        if (combinedBoundingBoxFilter.PassesFilter(s))
                        {
                            _spaces.Add(s);
                        }
                    }

                    if (_spaces.Count > 0)
                    {
                        return _spaces;
                    }
                }
            }

            return spaces;
        }


        public static IEnumerable<Room> FilterRoomsByLevels(IEnumerable<Level> levelsToFilter, IEnumerable<Room> rooms)
        {
            var _rooms = new List<Room>();

            //  Filter by Level if _levelsToFilter is not null
            if (levelsToFilter != null && levelsToFilter.Any())
            {
                var levelIdsToFilter = new HashSet<ElementId>(levelsToFilter.Select(level => level.Id));

                // Create ElementLevelFilters for all level IDs
                var levelFilters = levelIdsToFilter
                    .Select(id => (ElementFilter)new ElementLevelFilter(id)) // Cast each filter to ElementFilter
                    .ToList(); // Convert to List<ElementFilter>, which implements IList<ElementFilter>

                // Combine all level filters into a LogicalOrFilter
                var combinedLevelFilter = new LogicalOrFilter(levelFilters);

                foreach (var r in rooms)
                {
                    if (combinedLevelFilter.PassesFilter(r))
                    {
                        _rooms.Add(r);
                    }
                }

                if (_rooms.Count > 0)
                {
                    return _rooms;
                }
            }

            return rooms;
        }

        public static IEnumerable<Space> FilterSpacesByLevels(IEnumerable<Level> levelsToFilter, IEnumerable<Space> spaces)
        {
            var _spaces = new List<Space>();

            //  Filter by Level if _levelsToFilter is not null
            if (levelsToFilter != null && levelsToFilter.Any())
            {
                var levelIdsToFilter = new HashSet<ElementId>(levelsToFilter.Select(level => level.Id));

                // Create ElementLevelFilters for all level IDs
                var levelFilters = levelIdsToFilter
                    .Select(id => (ElementFilter)new ElementLevelFilter(id)) // Cast each filter to ElementFilter
                    .ToList(); // Convert to List<ElementFilter>, which implements IList<ElementFilter>

                // Combine all level filters into a LogicalOrFilter
                var combinedLevelFilter = new LogicalOrFilter(levelFilters);

                foreach (var s in spaces)
                {
                    if (combinedLevelFilter.PassesFilter(s))
                    {
                        _spaces.Add(s);
                    }
                }

                if (_spaces.Count > 0)
                { 
                    return _spaces; 
                }

            }

            return spaces;
        }

        public static IList<Element> FilterElementsByLevels(IEnumerable<Level> levelsToFilter, IList<Element> elements)
        {
            //  Filter by Level if _levelsToFilter is not null
            if (levelsToFilter != null && levelsToFilter.Any())
            {
                var levelIdsToFilter = new HashSet<ElementId>(levelsToFilter.Select(level => level.Id));

                // Create ElementLevelFilters for all level IDs
                var levelFilters = levelIdsToFilter
                    .Select(id => (ElementFilter)new ElementLevelFilter(id)) // Cast each filter to ElementFilter
                    .ToList(); // Convert to List<ElementFilter>, which implements IList<ElementFilter>

                // Combine all level filters into a LogicalOrFilter
                var combinedLevelFilter = new LogicalOrFilter(levelFilters);

                // Filter and return rooms that pass the combined filter
                var filteredElements = elements.Where(e =>
                {
                    // Check if the room passes the combined bounding box filter
                    return combinedLevelFilter.PassesFilter(e);
                });
            }

            return elements;
        }
    }
}
